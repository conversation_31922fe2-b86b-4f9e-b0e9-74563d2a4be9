{"name": "Equinx_Mobile_app", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-navigation/bottom-tabs": "^7.3.9", "@react-navigation/native": "^7.1.5", "@react-navigation/native-stack": "^7.3.9", "@reduxjs/toolkit": "^2.6.1", "@stripe/stripe-react-native": "^0.47.0", "axios": "^1.8.4", "dayjs": "^1.11.13", "libphonenumber-js": "^1.12.8", "moment-timezone": "^0.5.48", "react": "19.0.0", "react-i18next": "^15.5.2", "react-native": "0.78.2", "react-native-country-picker-modal": "^2.0.0", "react-native-date-picker": "^5.0.12", "react-native-device-info": "^14.0.4", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "^2.25.0", "react-native-image-picker": "^8.2.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.4.1", "react-native-reanimated": "^3.17.3", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.11.2", "react-native-svg-transformer": "^1.5.0", "react-native-toast-message": "^2.3.0", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.2", "@react-native/eslint-config": "0.78.2", "@react-native/metro-config": "0.78.2", "@react-native/typescript-config": "0.78.2", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-network-logger": "^2.0.0", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}