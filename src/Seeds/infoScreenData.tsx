import ICONS from "../Assets/Icons";
import { isIOS } from "../Utilities/Metrics";

const InfoScreenData = [
  {
    image: ICONS.Info1,
    title: "Reach your weight goal",
    subTitle:
      "Lose weight and maintain your new weight with a sustainable lifestyle tailored to you.",
    nextScreen: "questionScreen",
  },
  {
    image: ICONS.Info2,
    title: "",
    subTitle: "",
    nextScreen: "infoScreen",
  },
  {
    image: ICONS.Info3,
    title: "Let’s fine-tune your fasting experience!",
    subTitle: "Just a few more questions to personalize your journey.",
    nextScreen: "questionScreen",
  },
  {
    image: ICONS.Info4,
    title: "Chat with AI",
    subTitle:
      "Chat with our AI to effortlessly and enhance your nutritional journey.",
    nextScreen: "questionScreen",
  },
  {
    image: ICONS.Info5,
    title: "Learn how to track nutritional values using QR code scanning.",
    subTitle:
      "Use our QR code scanner to quickly log your meals and stay on top of your nutrition.",
    nextScreen: "questionScreen",
  },
  {
    image: ICONS.Info6,
    title: "Learn how to track your calorie intake.",
    subTitle:
      "A comprehensive guide to monitoring your daily calorie consumption for better health.",
    nextScreen: "questionScreen",
  },
  {
    image: ICONS.Info7,
    title:
      "Users who allow notifications see a 45% increase in their success rate",
    subTitle: "Let’s enable notifications and embark on this journey together.",
    nextScreen: isIOS ? "infoScreen" : "planScreen",
  },
  isIOS && {
    image: ICONS.Info8,
    title: "Unlock Insights with Apple Health",
    subTitle:
      "Enable this feature for smarter tracking, deeper insights, and a more personalized fasting experience tailored to your health goals.",
    nextScreen: "planScreen",
  },
];

export default InfoScreenData;
