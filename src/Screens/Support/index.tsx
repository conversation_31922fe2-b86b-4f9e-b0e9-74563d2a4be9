import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import React, { FC, useState } from "react";
import { SupportScreenProps } from "../../Typings/route";
import COLORS from "../../Utilities/Colors";
import { SafeAreaView } from "react-native-safe-area-context";
import { horizontalScale, verticalScale } from "../../Utilities/Metrics";
import CustomIcon from "../../Components/CustomIcon";
import { CustomText } from "../../Components/CustomText";
import ICONS from "../../Assets/Icons";

const questionData = [
  {
    id: 1,
    title: "1.  How do I start my first fasting session?",
    description:
      'Simply select your fasting schedule from the home screen and tap "Start Fast." The timer will track your progress.',
  },
  {
    id: 2,
    title: "2.  Can I change my fasting schedule?",
    description:
      'Simply select your fasting schedule from the home screen and tap "Start Fast." The timer will track your progress.',
  },
  {
    id: 3,
    title: "3.  How are my meal plans personalized?",
    description:
      'Simply select your fasting schedule from the home screen and tap "Start Fast." The timer will track your progress.',
  },
  {
    id: 4,
    title: "4.  Do I need a subscription to access meal plans?",
    description:
      'Simply select your fasting schedule from the home screen and tap "Start Fast." The timer will track your progress.',
  },
  {
    id: 5,
    title: "5.  How do I manage or cancel my subscription?",
    description:
      'Simply select your fasting schedule from the home screen and tap "Start Fast." The timer will track your progress.',
  },
  {
    id: 6,
    title: "6.  Is there a free version of the app?",
    description:
      'Simply select your fasting schedule from the home screen and tap "Start Fast." The timer will track your progress.',
  },
  {
    id: 7,
    title: "7.  How do I sync Apple Health with the app?",
    description:
      'Simply select your fasting schedule from the home screen and tap "Start Fast." The timer will track your progress.',
  },
];

const Support: FC<SupportScreenProps> = ({ navigation }) => {
  const [selectedFaq, setSelectedFaq] = useState<number | null>(null);

  const toggleFaq = (id: number) => {
    setSelectedFaq((prev) => (prev === id ? null : id));
  };
  return (
    <ScrollView
      style={{ flex: 1, backgroundColor: COLORS.white }}
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}
      bounces={false}
    >
      <SafeAreaView style={styles.safeAreaContainer}>
        <View style={styles.arrowContainer}>
          <TouchableOpacity
            onPress={() => {
              navigation.goBack();
            }}
          >
            <CustomIcon Icon={ICONS.BackArrow} />
          </TouchableOpacity>
          <CustomText fontSize={22} fontFamily="bold" color={COLORS.darkBLue}>
            Support
          </CustomText>
        </View>
        <View style={{ gap: verticalScale(6) }}>
          <CustomText fontFamily="bold" fontSize={12} color={COLORS.darkBLue}>
            Reach Us
          </CustomText>
          <TouchableOpacity style={styles.contactContainer}>
            <View style={styles.iconBg}>
              <CustomIcon Icon={ICONS.emailIcon} height={18} width={18} />
            </View>
            <View style={{ gap: verticalScale(4) }}>
              <CustomText
                fontSize={10}
                fontFamily="regular"
                color={COLORS.slateGrey}
              >
                Send us an email
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="regular"
                color={COLORS.darkBLue}
              >
                <EMAIL>
              </CustomText>
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={styles.contactContainer}>
            <View style={styles.iconBg}>
              <CustomIcon Icon={ICONS.callIcon} height={18} width={18} />
            </View>
            <View style={{ gap: verticalScale(4) }}>
              <CustomText
                fontSize={10}
                fontFamily="regular"
                color={COLORS.slateGrey}
              >
                Call Us
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="regular"
                color={COLORS.darkBLue}
              >
                +1 (555) 567-8901
              </CustomText>
            </View>
          </TouchableOpacity>
        </View>

        <View style={{ gap: verticalScale(6) }}>
          <CustomText fontSize={12} fontFamily="bold" color={COLORS.darkBLue}>
            Frequently Asked Questions
          </CustomText>
          {questionData.map((item, index) => (
            <View style={styles.questionWrapper} key={index}>
              <TouchableOpacity
                style={styles.questionBtn}
                onPress={() => toggleFaq(item.id)}
              >
                <CustomText
                  fontSize={14}
                  fontFamily="semiBold"
                  color={COLORS.darkBLue}
                >
                  {item.title}
                </CustomText>

                <CustomIcon
                  Icon={
                    selectedFaq === item.id
                      ? ICONS.decrementIcon
                      : ICONS.incrementIcon
                  }
                  height={16}
                  width={16}
                />
              </TouchableOpacity>

              {selectedFaq === item.id && (
                <View style={{ paddingHorizontal: horizontalScale(10) }}>
                  <CustomText
                    fontSize={12}
                    fontFamily="regular"
                    color={COLORS.darkBLue}
                  >
                    {item.description}
                  </CustomText>
                </View>
              )}
            </View>
          ))}
        </View>
      </SafeAreaView>
    </ScrollView>
  );
};

export default Support;

const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingVertical: verticalScale(15),
    paddingHorizontal: horizontalScale(20),
    gap: horizontalScale(20),
  },
  arrowContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: horizontalScale(15),
  },
  contactContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: horizontalScale(10),
    paddingHorizontal: horizontalScale(8),
    paddingVertical: verticalScale(10),
    boxShadow: "rgba(33, 35, 38, 0.1) 0px 10px 10px -10px",
  },
  iconBg: {
    backgroundColor: COLORS.greyishWhite,
    paddingVertical: verticalScale(5),
    paddingHorizontal: horizontalScale(5),
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  questionBtn: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  questionWrapper: {
    boxShadow: "rgba(33, 35, 38, 0.1) 0px 10px 10px -10px",
    paddingHorizontal: horizontalScale(8),
    paddingVertical: verticalScale(10),
    gap: verticalScale(8),
  },
});
