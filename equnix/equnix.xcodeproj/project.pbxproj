// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXGroup section */
		185BA3532DD08095000C351A = {
			isa = PBXGroup;
			children = (
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXProject section */
		185BA3542DD08095000C351A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1620;
			};
			buildConfigurationList = 185BA3572DD08095000C351A /* Build configuration list for PBXProject "equnix" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 185BA3532DD08095000C351A;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			projectDirPath = "";
			projectRoot = "";
			targets = (
			);
		};
/* End PBXProject section */

/* Begin XCBuildConfiguration section */
		185BA3582DD08095000C351A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
			};
			name = Debug;
		};
		185BA3592DD08095000C351A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		185BA3572DD08095000C351A /* Build configuration list for PBXProject "equnix" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				185BA3582DD08095000C351A /* Debug */,
				185BA3592DD08095000C351A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 185BA3542DD08095000C351A /* Project object */;
}
